import { useState, useEffect, useMemo } from 'react';
import { Transaction, DashboardData } from '../types';
import { useTransactions } from './useTransactions';

export const useDashboard = () => {
  const { transactions, loading } = useTransactions();
  
  const dashboardData = useMemo((): DashboardData => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    // Filter transactions for current month
    const currentMonthTransactions = transactions.filter(transaction => {
      const transactionDate = new Date(transaction.date);
      return transactionDate.getMonth() === currentMonth && 
             transactionDate.getFullYear() === currentYear;
    });
    
    // Calculate totals
    const totalIncome = currentMonthTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const totalExpense = currentMonthTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const currentBalance = totalIncome - totalExpense;
    
    // Calculate monthly change (comparing to previous month)
    const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const previousYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    
    const previousMonthTransactions = transactions.filter(transaction => {
      const transactionDate = new Date(transaction.date);
      return transactionDate.getMonth() === previousMonth && 
             transactionDate.getFullYear() === previousYear;
    });
    
    const previousMonthBalance = previousMonthTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0) - 
      previousMonthTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const monthlyChange = previousMonthBalance === 0 ? 0 : 
      ((currentBalance - previousMonthBalance) / Math.abs(previousMonthBalance)) * 100;
    
    // Get recent transactions (last 10)
    const recentTransactions = transactions.slice(0, 10);
    
    // Generate monthly stats for the last 12 months
    const monthlyStats = [];
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const month = date.toLocaleString('default', { month: 'short' });
      const year = date.getFullYear();
      
      const monthTransactions = transactions.filter(transaction => {
        const transactionDate = new Date(transaction.date);
        return transactionDate.getMonth() === date.getMonth() && 
               transactionDate.getFullYear() === year;
      });
      
      const monthIncome = monthTransactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0);
      
      const monthExpense = monthTransactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0);
      
      monthlyStats.push({
        month,
        year,
        totalIncome: monthIncome,
        totalExpense: monthExpense,
        balance: monthIncome - monthExpense,
        transactionCount: monthTransactions.length
      });
    }
    
    // Default categories
    const categories = [
      { id: '1', name: 'Food & Dining', type: 'expense' as const, color: '#ef4444', icon: '🍽️' },
      { id: '2', name: 'Transportation', type: 'expense' as const, color: '#f59e0b', icon: '🚗' },
      { id: '3', name: 'Shopping', type: 'expense' as const, color: '#8b5cf6', icon: '🛍️' },
      { id: '4', name: 'Entertainment', type: 'expense' as const, color: '#06b6d4', icon: '🎬' },
      { id: '5', name: 'Bills & Utilities', type: 'expense' as const, color: '#84cc16', icon: '💡' },
      { id: '6', name: 'Salary', type: 'income' as const, color: '#10b981', icon: '💰' },
      { id: '7', name: 'Freelance', type: 'income' as const, color: '#3b82f6', icon: '💼' },
      { id: '8', name: 'Investments', type: 'income' as const, color: '#8b5cf6', icon: '📈' },
    ];
    
    // Default cards
    const cards = [
      {
        id: '1',
        userId: 'current-user',
        name: 'Main Card',
        lastFourDigits: '9876',
        type: 'visa' as const,
        color: '#667eea',
        isDefault: true
      }
    ];
    
    return {
      totalIncome,
      totalExpense,
      currentBalance,
      monthlyChange,
      recentTransactions,
      monthlyStats,
      categories,
      cards
    };
  }, [transactions]);
  
  return {
    dashboardData,
    loading
  };
};
