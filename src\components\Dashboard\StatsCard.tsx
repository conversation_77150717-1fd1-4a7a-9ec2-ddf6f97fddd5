import React from 'react';
import { formatCurrency, formatPercentage } from '../../utils/formatters';
import Card from '../UI/Card';

interface StatsCardProps {
  title: string;
  amount: number;
  change: number;
  changeType: 'positive' | 'negative';
  icon: React.ReactNode;
  subtitle?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  amount,
  change,
  changeType,
  icon,
  subtitle
}) => {
  return (
    <Card>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center mb-2">
            {icon}
            <h3 className="text-sm font-medium text-gray-600 ml-2">{title}</h3>
          </div>
          
          <div className="mb-2">
            <p className="text-2xl font-bold text-gray-900">
              {formatCurrency(amount)}
            </p>
          </div>
          
          <div className="flex items-center">
            <span
              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                changeType === 'positive'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {changeType === 'positive' ? '↗' : '↘'}
              {formatPercentage(change)}
            </span>
          </div>
          
          {subtitle && (
            <p className="text-xs text-gray-500 mt-2">{subtitle}</p>
          )}
        </div>
      </div>
    </Card>
  );
};

export default StatsCard;
