import React from 'react';
import { ArrowUpIcon, ArrowDownIcon, PlusIcon } from '@heroicons/react/24/outline';
import Card from '../components/UI/Card';
import Button from '../components/UI/Button';
import { useDashboard } from '../hooks/useDashboard';
import { formatCurrency } from '../utils/formatters';
import StatsCard from '../components/Dashboard/StatsCard';
import TransactionsList from '../components/Dashboard/TransactionsList';
import FinancialChart from '../components/Dashboard/FinancialChart';
import CreditCards from '../components/Dashboard/CreditCards';

const Dashboard: React.FC = () => {
  const { dashboardData, loading } = useDashboard();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  const {
    totalIncome,
    totalExpense,
    currentBalance,
    monthlyChange,
    recentTransactions,
    monthlyStats
  } = dashboardData;

  return (
    <div className="space-y-6">
      {/* Stats Cards Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatsCard
          title="Income"
          amount={totalIncome}
          change={38.64}
          changeType="positive"
          icon={<ArrowDownIcon className="w-5 h-5 text-green-600" />}
          subtitle="You made an extra $3,345.00 this month"
        />
        
        <StatsCard
          title="Expense"
          amount={totalExpense}
          change={32.73}
          changeType="negative"
          icon={<ArrowUpIcon className="w-5 h-5 text-red-600" />}
          subtitle="You overspent by $5256.00 this month"
        />
        
        <StatsCard
          title="My Balance"
          amount={currentBalance}
          change={Math.abs(monthlyChange)}
          changeType={monthlyChange >= 0 ? 'positive' : 'negative'}
          subtitle={`You made an extra $${Math.abs(currentBalance * 0.16).toFixed(2)} this month`}
        />
      </div>

      {/* Chart and Cards Row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Financial Chart */}
        <div className="lg:col-span-2">
          <Card>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Total Cash In and Cash Out</h3>
              <select className="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:outline-none focus:ring-2 focus:ring-purple-500">
                <option>This Year</option>
                <option>Last Year</option>
                <option>Last 6 Months</option>
              </select>
            </div>
            <FinancialChart data={monthlyStats} />
          </Card>
        </div>

        {/* Credit Cards */}
        <div>
          <CreditCards />
        </div>
      </div>

      {/* Recent Transactions */}
      <Card>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Recent Transaction</h3>
          <div className="flex items-center space-x-3">
            <div className="relative">
              <input
                type="text"
                placeholder="Search transactions"
                className="pl-8 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center">
                <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            <Button variant="secondary" size="sm">
              Filter
            </Button>
          </div>
        </div>
        <TransactionsList transactions={recentTransactions} />
      </Card>
    </div>
  );
};

export default Dashboard;
