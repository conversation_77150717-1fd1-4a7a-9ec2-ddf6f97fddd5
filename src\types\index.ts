export interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
}

export interface Transaction {
  id: string;
  userId: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  category: string;
  date: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Category {
  id: string;
  name: string;
  type: 'income' | 'expense';
  color: string;
  icon: string;
}

export interface Card {
  id: string;
  userId: string;
  name: string;
  lastFourDigits: string;
  type: 'visa' | 'mastercard' | 'amex' | 'other';
  color: string;
  isDefault: boolean;
}

export interface MonthlyStats {
  month: string;
  year: number;
  totalIncome: number;
  totalExpense: number;
  balance: number;
  transactionCount: number;
}

export interface DashboardData {
  totalIncome: number;
  totalExpense: number;
  currentBalance: number;
  monthlyChange: number;
  recentTransactions: Transaction[];
  monthlyStats: MonthlyStats[];
  categories: Category[];
  cards: Card[];
}

export interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, displayName: string) => Promise<void>;
  signOut: () => Promise<void>;
  signInWithGoogle: () => Promise<void>;
}

export interface TransactionFormData {
  type: 'income' | 'expense';
  amount: string;
  description: string;
  category: string;
  date: string;
}
