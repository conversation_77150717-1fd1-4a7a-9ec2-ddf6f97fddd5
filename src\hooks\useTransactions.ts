import { useState, useEffect } from 'react';
import { Transaction, TransactionFormData } from '../types';
import { transactionService } from '../services/transactionService';
import { useAuth } from '../contexts/AuthContext';

export const useTransactions = () => {
  const { user } = useAuth();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTransactions = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      const userTransactions = await transactionService.getUserTransactions(user.uid);
      setTransactions(userTransactions);
    } catch (err) {
      setError('Failed to fetch transactions');
      console.error('Error fetching transactions:', err);
    } finally {
      setLoading(false);
    }
  };

  const createTransaction = async (data: TransactionFormData) => {
    if (!user) throw new Error('User not authenticated');
    
    try {
      setError(null);
      await transactionService.createTransaction(user.uid, data);
      await fetchTransactions(); // Refresh the list
    } catch (err) {
      setError('Failed to create transaction');
      throw err;
    }
  };

  const updateTransaction = async (transactionId: string, data: Partial<TransactionFormData>) => {
    try {
      setError(null);
      await transactionService.updateTransaction(transactionId, data);
      await fetchTransactions(); // Refresh the list
    } catch (err) {
      setError('Failed to update transaction');
      throw err;
    }
  };

  const deleteTransaction = async (transactionId: string) => {
    try {
      setError(null);
      await transactionService.deleteTransaction(transactionId);
      await fetchTransactions(); // Refresh the list
    } catch (err) {
      setError('Failed to delete transaction');
      throw err;
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, [user]);

  return {
    transactions,
    loading,
    error,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    refetch: fetchTransactions
  };
};
