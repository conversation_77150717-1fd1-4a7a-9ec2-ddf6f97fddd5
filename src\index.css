@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-50 font-sans antialiased;
    margin: 0;
    min-height: 100vh;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-purple-600 to-blue-600 text-white font-medium py-2 px-4 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-white text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md;
  }

  .card {
    @apply bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all duration-200 p-6;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-purple-600 via-blue-600 to-purple-800;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-gray-700 hover:bg-purple-50 hover:text-purple-700 rounded-lg transition-all duration-200 cursor-pointer;
  }

  .sidebar-item.active {
    @apply bg-purple-100 text-purple-700 font-medium;
  }
}