import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  Timestamp
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { Transaction, TransactionFormData } from '../types';

export const transactionService = {
  // Create a new transaction
  async createTransaction(userId: string, data: TransactionFormData): Promise<string> {
    try {
      const transactionData = {
        userId,
        type: data.type,
        amount: parseFloat(data.amount),
        description: data.description,
        category: data.category,
        date: Timestamp.fromDate(new Date(data.date)),
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      const docRef = await addDoc(collection(db, 'transactions'), transactionData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating transaction:', error);
      throw error;
    }
  },

  // Get user transactions
  async getUserTransactions(userId: string, limitCount: number = 50): Promise<Transaction[]> {
    try {
      const q = query(
        collection(db, 'transactions'),
        where('userId', '==', userId),
        orderBy('date', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const transactions: Transaction[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        transactions.push({
          id: doc.id,
          userId: data.userId,
          type: data.type,
          amount: data.amount,
          description: data.description,
          category: data.category,
          date: data.date.toDate(),
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate()
        });
      });

      return transactions;
    } catch (error) {
      console.error('Error getting transactions:', error);
      throw error;
    }
  },

  // Update transaction
  async updateTransaction(transactionId: string, data: Partial<TransactionFormData>): Promise<void> {
    try {
      const updateData: any = {
        updatedAt: Timestamp.now()
      };

      if (data.type) updateData.type = data.type;
      if (data.amount) updateData.amount = parseFloat(data.amount);
      if (data.description) updateData.description = data.description;
      if (data.category) updateData.category = data.category;
      if (data.date) updateData.date = Timestamp.fromDate(new Date(data.date));

      await updateDoc(doc(db, 'transactions', transactionId), updateData);
    } catch (error) {
      console.error('Error updating transaction:', error);
      throw error;
    }
  },

  // Delete transaction
  async deleteTransaction(transactionId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'transactions', transactionId));
    } catch (error) {
      console.error('Error deleting transaction:', error);
      throw error;
    }
  },

  // Get transactions by date range
  async getTransactionsByDateRange(
    userId: string,
    startDate: Date,
    endDate: Date
  ): Promise<Transaction[]> {
    try {
      const q = query(
        collection(db, 'transactions'),
        where('userId', '==', userId),
        where('date', '>=', Timestamp.fromDate(startDate)),
        where('date', '<=', Timestamp.fromDate(endDate)),
        orderBy('date', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const transactions: Transaction[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        transactions.push({
          id: doc.id,
          userId: data.userId,
          type: data.type,
          amount: data.amount,
          description: data.description,
          category: data.category,
          date: data.date.toDate(),
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate()
        });
      });

      return transactions;
    } catch (error) {
      console.error('Error getting transactions by date range:', error);
      throw error;
    }
  }
};
