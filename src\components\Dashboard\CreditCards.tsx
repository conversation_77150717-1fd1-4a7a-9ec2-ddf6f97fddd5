import React from 'react';
import { PlusIcon } from '@heroicons/react/24/outline';
import Card from '../UI/Card';
import Button from '../UI/Button';

const CreditCards: React.FC = () => {
  return (
    <div className="space-y-4">
      {/* Balance Card */}
      <Card>
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">My Balance</h3>
          <p className="text-3xl font-bold text-gray-900 mb-1">$87,376.00</p>
          <div className="flex items-center justify-center">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              ↗ 58.42%
            </span>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            You made an extra $14,972.00 this month
          </p>
        </div>
      </Card>

      {/* My Cards Section */}
      <Card>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">My Cards</h3>
          <Button variant="secondary" size="sm">
            <PlusIcon className="w-4 h-4 mr-1" />
            Add card
          </Button>
        </div>

        {/* Credit Card */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-4 text-white mb-4">
          <div className="flex justify-between items-start mb-8">
            <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">P</span>
            </div>
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-white bg-opacity-60 rounded-full"></div>
              <div className="w-2 h-2 bg-white bg-opacity-60 rounded-full"></div>
              <div className="w-2 h-2 bg-white bg-opacity-60 rounded-full"></div>
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
          </div>
          
          <div className="mb-4">
            <p className="text-sm opacity-80">Kianna Saris</p>
            <p className="text-lg font-mono">•••• •••• 9876</p>
          </div>
          
          <div className="flex justify-between items-center">
            <div>
              <p className="text-xs opacity-80">Exp 06/24</p>
            </div>
            <div className="text-right">
              <p className="text-lg font-bold">VISA</p>
            </div>
          </div>
        </div>

        {/* Card Actions */}
        <div className="grid grid-cols-4 gap-2">
          <button className="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-2">
              <svg className="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
            </div>
            <span className="text-xs text-gray-600">Convert</span>
          </button>
          
          <button className="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mb-2">
              <svg className="w-5 h-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </div>
            <span className="text-xs text-gray-600">Send</span>
          </button>
          
          <button className="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mb-2">
              <svg className="w-5 h-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <span className="text-xs text-gray-600">Receive</span>
          </button>
          
          <button className="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mb-2">
              <svg className="w-5 h-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
              </svg>
            </div>
            <span className="text-xs text-gray-600">More</span>
          </button>
        </div>
      </Card>
    </div>
  );
};

export default CreditCards;
