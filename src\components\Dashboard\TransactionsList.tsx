import React from 'react';
import { Transaction } from '../../types';
import { formatCurrency, formatDate, formatTime } from '../../utils/formatters';

interface TransactionsListProps {
  transactions: Transaction[];
}

const TransactionsList: React.FC<TransactionsListProps> = ({ transactions }) => {
  const getTransactionIcon = (category: string) => {
    const icons: { [key: string]: string } = {
      'Food & Dining': '🍽️',
      'Transportation': '🚗',
      'Shopping': '🛍️',
      'Entertainment': '🎬',
      'Bills & Utilities': '💡',
      'Salary': '💰',
      'Freelance': '💼',
      'Investments': '📈',
      'Subscriptions': '📱',
      'Other': '💳'
    };
    return icons[category] || '💳';
  };

  const getStatusBadge = (type: string) => {
    return type === 'income' ? (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
        Success
      </span>
    ) : (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
        Expense
      </span>
    );
  };

  if (transactions.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions yet</h3>
        <p className="text-gray-500">Start by adding your first transaction</p>
      </div>
    );
  }

  return (
    <div className="overflow-hidden">
      {/* Table Header */}
      <div className="grid grid-cols-12 gap-4 px-4 py-3 bg-gray-50 rounded-t-lg text-sm font-medium text-gray-700">
        <div className="col-span-4">Transaction</div>
        <div className="col-span-2">Date</div>
        <div className="col-span-2">Amount</div>
        <div className="col-span-2">Category</div>
        <div className="col-span-1">Account</div>
        <div className="col-span-1">Status</div>
      </div>

      {/* Transactions */}
      <div className="divide-y divide-gray-200">
        {transactions.map((transaction) => (
          <div key={transaction.id} className="grid grid-cols-12 gap-4 px-4 py-4 hover:bg-gray-50 transition-colors">
            {/* Transaction Info */}
            <div className="col-span-4 flex items-center">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                <span className="text-lg">{getTransactionIcon(transaction.category)}</span>
              </div>
              <div>
                <p className="font-medium text-gray-900">{transaction.description}</p>
                <p className="text-sm text-gray-500">{formatTime(transaction.date)}</p>
              </div>
            </div>

            {/* Date */}
            <div className="col-span-2 flex items-center">
              <p className="text-sm text-gray-900">{formatDate(transaction.date)}</p>
            </div>

            {/* Amount */}
            <div className="col-span-2 flex items-center">
              <p className={`font-medium ${
                transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
              }`}>
                {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
              </p>
            </div>

            {/* Category */}
            <div className="col-span-2 flex items-center">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                {transaction.category}
              </span>
            </div>

            {/* Account */}
            <div className="col-span-1 flex items-center">
              <div className="flex items-center">
                <div className="w-6 h-4 bg-blue-600 rounded-sm flex items-center justify-center mr-1">
                  <span className="text-white text-xs font-bold">V</span>
                </div>
                <span className="text-sm text-gray-600">9876</span>
              </div>
            </div>

            {/* Status */}
            <div className="col-span-1 flex items-center">
              {getStatusBadge(transaction.type)}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TransactionsList;
